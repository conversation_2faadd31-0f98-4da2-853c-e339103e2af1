import userApi from "../../api/modules/user";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 用户信息
    userInfo: {
      type: Object,
      value: null
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 表单数据
    formData: {
      nickname: '',
      avatar: ''
    },
    // 默认头像
    defaultAvatar: 'https://xian7.zos.ctyun.cn/pet/static/defalutAvatar1.png',
    // 昵称错误提示
    nicknameError: '',
    // 是否正在加载
    loading: false
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 关闭弹窗
     */
    closeModal() {
      this.triggerEvent('cancel');
    },

    /**
     * 昵称输入处理
     */
    onNicknameInput(e) {
      const value = e.detail.value.trim();
      this.setData({
        'formData.nickname': value,
        nicknameError: ''
      });
      
      // 实时验证昵称
      this.validateNickname(value);
    },

    /**
     * 验证昵称
     */
    validateNickname(nickname) {
      let error = '';
      
      if (!nickname) {
        error = '请输入昵称';
      } else if (nickname.length < 2) {
        error = '昵称至少2个字符';
      } else if (nickname.length > 12) {
        error = '昵称最多12个字符';
      } else if (/[<>]/.test(nickname)) {
        error = '昵称包含非法字符';
      }
      
      this.setData({ nicknameError: error });
      return !error;
    },

    /**
     * 选择头像
     */
    chooseAvatar() {
      const that = this;

      // 获取页面实例来调用上传方法
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];

      if (!currentPage || !currentPage.uploadImage) {
        wx.showToast({
          title: '上传功能不可用',
          icon: 'none'
        });
        return;
      }

      // 直接调用页面的上传方法
      currentPage.uploadImage(
        currentPage,
        '', // 字段名
        'avatar/', // 上传前缀
        1 // 数量
      ).then(res => {
        if (res && res.length > 0) {
          that.setData({
            'formData.avatar': res[0]
          });
          wx.showToast({
            title: '头像上传成功',
            icon: 'success'
          });
        }
      }).catch(error => {
        wx.showToast({
          title: '头像上传失败',
          icon: 'none'
        });
        console.error('头像上传失败:', error);
      });
    },

    /**
     * 确认编辑
     */
    async confirmEdit() {
      const { formData, loading } = this.data;
      const { userInfo } = this.properties;

      // 如果正在加载中，防止重复提交
      if (loading) {
        return;
      }

      // 验证昵称
      if (!this.validateNickname(formData.nickname)) {
        return;
      }

      // 检查是否有修改
      const hasNicknameChange = formData.nickname !== userInfo.nickname;
      const hasAvatarChange = formData.avatar && formData.avatar !== userInfo.avatar;
      
      if (!hasNicknameChange && !hasAvatarChange) {
        wx.showToast({
          title: '没有修改内容',
          icon: 'none'
        });
        return;
      }

      try {
        this.setData({ loading: true });
        wx.showLoading({ title: '保存中...' });

        // 准备更新数据
        const updateData = {};

        if (hasNicknameChange) {
          updateData.name = formData.nickname;
        }

        if (hasAvatarChange) {
          updateData.avatar = formData.avatar;
        }

        // 调用更新接口
        await userApi.updateProfile(updateData);

        wx.hideLoading();
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        });

        // 触发确认事件，将更新的数据传递给父组件
        const parentUpdateData = {};
        if (hasNicknameChange) {
          parentUpdateData.nickname = formData.nickname;
        }
        if (hasAvatarChange) {
          parentUpdateData.avatar = formData.avatar;
        }

        this.triggerEvent('confirm', {
          updatedData: parentUpdateData
        });

      } catch (error) {
        wx.hideLoading();
        wx.showToast({
          title: error.message || '保存失败，请稍后重试',
          icon: 'none'
        });
        console.error('更新用户信息失败:', error);
      } finally {
        this.setData({ loading: false });
      }
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'show': function(show) {
      if (show && this.properties.userInfo) {
        // 弹窗显示时初始化表单数据
        this.setData({
          formData: {
            nickname: this.properties.userInfo.nickname || '',
            avatar: this.properties.userInfo.avatar || ''
          },
          nicknameError: '',
          loading: false
        });
      }
    }
  }
});
