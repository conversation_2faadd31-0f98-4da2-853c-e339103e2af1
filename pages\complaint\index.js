// pages/complaint/index.js
import complaintApi from '../../api/modules/complaint';
import orderApi from '../../api/modules/order';
import Session from '../../common/Session';
import { OrderStatus } from '../../common/constant';


Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    // 表单数据
    category: '', // 大类：complaint/suggestion
    subCategory: '', // 小类：order/employee/platform/service
    title: '',
    content: '',
    contactInfo: '',
    orderId: '', // 关联订单ID（可选）
    photoList: [], // 图片列表
    
    // 分类选项
    categoryOptions: [
      { value: 'complaint', label: '投诉', img: '//xian7.zos.ctyun.cn/pet/static/tou-su.png', icon: '📢' },
      { value: 'suggestion', label: '建议', img: '//xian7.zos.ctyun.cn/pet/static/jian-yi.png', icon: '💡' }
    ],
    subCategoryOptions: {
      complaint: [
        { value: 'order', label: '订单投诉', desc: '针对具体订单的投诉' },
        { value: 'employee', label: '人员投诉', desc: '针对服务人员的投诉' }
      ],
      suggestion: [
        { value: 'platform', label: '平台建议', desc: '对平台功能、体验的改进建议' },
        { value: 'service', label: '服务建议', desc: '对服务内容、流程的改进建议' }
      ]
    },
    
    // 订单相关
    showOrderSelector: false,
    orderList: [],
    selectedOrder: null,
    
    // UI状态
    canSubmit: false,
    isSubmitting: false,

    // 编辑模式
    isEditMode: false,
    editComplaintId: '',

    // 验证状态
    contactError: '',
    
    // 字符计数
    titleMaxLength: 200,
    contentMaxLength: 2000,
    
    // 提示文本
    placeholders: {
      complaint: {
        order: '请详细描述您遇到的订单问题，我们会尽快为您处理',
        employee: '请详细描述服务人员的问题，我们会认真核实并处理'
      },
      suggestion: {
        platform: '请分享您对平台的改进建议，帮助我们提供更好的服务',
        service: '请分享您对服务的改进建议，我们会认真考虑并优化'
      }
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = Session.getUser();
    this.setData({ userInfo });

    // 检查是否为编辑模式
    if (options.mode === 'edit' && options.id) {
      this.setData({
        isEditMode: true,
        editComplaintId: options.id
      });
      this.loadComplaintForEdit(options.id);
    } else {
      // 如果从订单页面跳转过来，预设订单ID
      if (options.orderId) {
        this.setData({
          orderId: options.orderId,
          category: 'complaint',
          subCategory: 'order'
        });
        this.loadOrderDetail(options.orderId);
      }

      // 预设联系方式
      if (userInfo && userInfo.phone) {
        this.setData({ contactInfo: userInfo.phone });
      }
    }
  },

  /**
   * 加载投诉建议数据进行编辑
   */
  async loadComplaintForEdit(complaintId) {
    try {
      wx.showLoading({ title: '加载中...' });

      const { userInfo } = this.data;
      const complaintDetail = await complaintApi.detail(userInfo.id, complaintId);

      if (complaintDetail) {
        // 填充表单数据
        this.setData({
          category: complaintDetail.category,
          subCategory: complaintDetail.subCategory,
          title: complaintDetail.title,
          content: complaintDetail.content,
          contactInfo: complaintDetail.contactInfo || '',
          orderId: complaintDetail.orderId || '',
          photoList: complaintDetail.photoURLs || []
        });

        // 如果有关联订单，加载订单详情
        if (complaintDetail.orderId) {
          this.loadOrderDetail(complaintDetail.orderId);
        }

        // 验证联系方式和检查提交条件
        this.validateContact();
        this.checkCanSubmit();
      } else {
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('加载投诉建议失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 选择大类
   */
  selectCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      category,
      subCategory: '', // 重置小类
      orderId: '',
      selectedOrder: null
    }, () => {
      this.validateContact(); // 重新验证联系方式
      this.checkCanSubmit();
    });
  },

  /**
   * 选择小类
   */
  selectSubCategory(e) {
    const subCategory = e.currentTarget.dataset.subcategory;
    this.setData({ subCategory }, () => {
      // 如果选择订单投诉，显示订单选择器
      if (subCategory === 'order') {
        this.loadOrderList();
      }
      this.checkCanSubmit();
    });
  },

  /**
   * 标题输入
   */
  onTitleInput(e) {
    this.setData({ title: e.detail.value }, () => {
      this.checkCanSubmit();
    });
  },

  /**
   * 内容输入
   */
  onContentInput(e) {
    this.setData({ content: e.detail.value }, () => {
      this.checkCanSubmit();
    });
  },

  /**
   * 联系方式输入
   */
  onContactInput(e) {
    const contactInfo = e.detail.value;
    this.setData({ contactInfo }, () => {
      this.validateContact();
      this.checkCanSubmit();
    });
  },

  /**
   * 验证联系方式
   */
  validateContact() {
    const { category, contactInfo } = this.data;
    let contactError = '';

    // 投诉必须提供联系方式
    if (category === 'complaint') {
      if (!contactInfo.trim()) {
        contactError = '投诉必须提供联系方式';
      } else if (!this.isValidPhone(contactInfo.trim())) {
        contactError = '请输入正确的手机号码';
      }
    }
    // 建议可以不提供联系方式，但如果提供了需要验证格式
    else if (category === 'suggestion' && contactInfo.trim()) {
      if (!this.isValidPhone(contactInfo.trim())) {
        contactError = '请输入正确的手机号码';
      }
    }

    this.setData({ contactError });
    return !contactError;
  },

  /**
   * 验证手机号格式
   */
  isValidPhone(phone) {
    return /^1[3456789]\d{9}$/.test(phone);
  },

  /**
   * 检查是否可以提交
   */
  checkCanSubmit() {
    const { category, subCategory, title, content, contactInfo, contactError } = this.data;

    // 基本字段检查
    let canSubmit = category && subCategory && title.trim() && content.trim();

    // 联系方式检查
    if (category === 'complaint') {
      // 投诉必须提供联系方式且格式正确
      canSubmit = canSubmit && contactInfo.trim() && !contactError;
    } else if (category === 'suggestion') {
      // 建议可以不提供联系方式，但如果提供了必须格式正确
      canSubmit = canSubmit && !contactError;
    }

    this.setData({ canSubmit });
  },

  /**
   * 加载订单列表
   */
  async loadOrderList() {
    try {
      const { userInfo } = this.data;
      if (!userInfo) return;
      
      const orderList = await orderApi.getlists(userInfo.id, `${OrderStatus.服务中},${OrderStatus.已完成},${OrderStatus.已评价}`);
      this.setData({ 
        orderList: orderList || [],
        showOrderSelector: true 
      });
    } catch (error) {
      console.error('加载订单列表失败:', error);
    }
  },

  /**
   * 加载订单详情
   */
  async loadOrderDetail(orderId) {
    try {
      const { userInfo } = this.data;
      if (!userInfo) return;
      
      const orderDetail = await orderApi.getDetail(userInfo.id, orderId);
      this.setData({ selectedOrder: orderDetail });
    } catch (error) {
      console.error('加载订单详情失败:', error);
    }
  },

  /**
   * 选择订单
   */
  selectOrder(e) {
    const order = e.currentTarget.dataset.order;
    this.setData({
      orderId: order.id,
      selectedOrder: order,
      showOrderSelector: false
    });
  },

  /**
   * 取消选择订单
   */
  cancelOrderSelection() {
    this.setData({
      showOrderSelector: false
    });
  },

  /**
   * 清除已选订单
   */
  clearSelectedOrder() {
    this.setData({
      orderId: '',
      selectedOrder: null
    });
  },

  /**
   * 选择图片
   */
  chooseImage() {
    const { photoList, userInfo } = this.data;
    const remainCount = 6 - photoList.length;
    // 使用现有的上传功能
    this.uploadImage(
      this,
      '', // 存储字段
      `complaint/${userInfo.id}-${userInfo.nickName}/`, // 上传key前缀
      remainCount // 最大数量
    ).then(res => {
      this.setData({
        photoList: [...photoList, ...res],
      });
    });        
  },

  /**
   * 删除图片
   */
  deletePhoto(e) {
    const index = e.currentTarget.dataset.index;
    const photoList = [...this.data.photoList];
    photoList.splice(index, 1);
    this.setData({ photoList });
  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    wx.previewImage({
      current: url,
      urls: this.data.photoList
    });
  },

  /**
   * 查看历史记录
   */
  viewHistory() {
    wx.navigateTo({
      url: '/pages/complaint/list'
    });
  },

  /**
   * 提交投诉建议
   */
  async submitComplaint() {
    const {
      userInfo, category, subCategory, title, content, contactInfo,
      orderId, photoList, canSubmit, isEditMode, editComplaintId
    } = this.data;

    if (!canSubmit) {
      wx.showToast({
        title: '请完善信息',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSubmitting: true });

    try {
      const complaintData = {
        category,
        subCategory,
        title: title.trim(),
        content: content.trim(),
        contactInfo: contactInfo.trim(),
        photoURLs: photoList
      };

      // 如果是订单投诉且选择了订单，添加订单ID
      if (subCategory === 'order' && orderId) {
        complaintData.orderId = parseInt(orderId);
      }

      let res;
      if (isEditMode) {
        // 编辑模式：更新投诉建议
        res = await complaintApi.update(userInfo.id, editComplaintId, complaintData);
      } else {
        // 创建模式：新建投诉建议
        res = await complaintApi.create(userInfo.id, complaintData);
      }

      if (!res) {
        wx.showToast({
          title: `${isEditMode ? '更新' : '提交'}失败，请稍后重试`,
          icon: 'none'
        });
        return;
      }

      wx.showToast({
        title: `${isEditMode ? '更新' : '提交'}成功`,
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } catch (error) {
      console.error(`${isEditMode ? '更新' : '提交'}投诉建议失败:`, error);
      wx.showToast({
        title: `${isEditMode ? '更新' : '提交'}失败，请重试`,
        icon: 'none'
      });
    } finally {
      this.setData({ isSubmitting: false });
    }
  }
});
